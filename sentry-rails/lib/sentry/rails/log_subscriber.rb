# frozen_string_literal: true

require "active_support/log_subscriber"

module Sentry
  module Rails
    # Base class for Sentry log subscribers that extends ActiveSupport::LogSubscriber
    # to provide structured logging capabilities for Rails components.
    #
    # This class follows Rails' LogSubscriber pattern and provides common functionality
    # for capturing Rails instrumentation events and logging them through Sentry's
    # structured logging system.
    #
    # @example Creating a custom log subscriber
    #   class MySubscriber < Sentry::Rails::LogSubscriber
    #     attach_to :my_component
    #
    #     def my_event(event)
    #       log_structured_event(
    #         message: "My event occurred",
    #         level: :info,
    #         attributes: {
    #           duration_ms: event.duration,
    #           custom_data: event.payload[:custom_data]
    #         }
    #       )
    #     end
    #   end
    class LogSubscriber < ActiveSupport::LogSubscriber
      class << self
        unless ActiveSupport::LogSubscriber.method_defined?(:detach_from)
          # Detach the subscriber from a namespace.
          # This method provides backward compatibility for Rails versions < 6.0
          # where ActiveSupport::Subscriber#detach_from was not available.
          #
          # For Rails >= 6.0, this delegates to the native Rails implementation.
          # For Rails < 6.0, this implements the detach functionality by:
          # 1. Removing the subscriber from the subscribers list
          # 2. Unsubscribing the entire subscriber from notifications
          #
          # @param namespace [Symbol] The namespace to detach from
          # @param notifier [ActiveSupport::Notifications] The notifier to use
          def detach_from(namespace, notifier = ActiveSupport::Notifications)
            @namespace = namespace
            @subscriber = find_attached_subscriber
            @notifier = notifier

            return unless @subscriber

            # Remove the subscriber from the subscribers list
            subscribers.delete(@subscriber)

            # Unsubscribe the entire subscriber object from notifications
            # This works cleanly across all Rails versions
            @notifier.unsubscribe(@subscriber)

            # Reset notifier so that event subscribers will not add for new methods
            @notifier = nil
          end

          private

          # Find the attached subscriber instance for this class
          def find_attached_subscriber
            return nil unless respond_to?(:subscribers)

            # Look for an instance of this exact class
            # Note: self here refers to the class that detach_from was called on
            subscribers.find { |subscriber| subscriber.class == self }
          end


        end
      end

      protected

      # Log a structured event using Sentry's structured logger
      #
      # @param message [String] The log message
      # @param level [Symbol] The log level (:trace, :debug, :info, :warn, :error, :fatal)
      # @param attributes [Hash] Additional structured attributes to include
      def log_structured_event(message:, level: :info, attributes: {})
        return unless Sentry.configuration.enable_logs

        Sentry.logger.public_send(level, message, **attributes)
      rescue => e
        # Silently handle any errors in logging to avoid breaking the application
        Sentry.configuration.sdk_logger.debug("Failed to log structured event: #{e.message}")
      end

      # Check if an event should be excluded from logging
      #
      # @param event [ActiveSupport::Notifications::Event] The event to check
      # @return [Boolean] true if the event should be excluded
      def excluded_event?(event)
        # Skip Rails' internal events
        return true if event.name.start_with?("!")

        false
      end

      # Calculate duration in milliseconds from an event
      #
      # @param event [ActiveSupport::Notifications::Event] The event
      # @return [Float] Duration in milliseconds
      def duration_ms(event)
        event.duration.round(2)
      end
    end
  end
end
